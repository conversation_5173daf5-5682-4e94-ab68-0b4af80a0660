"""
翻译API接口层 - 简化版本，只保留上传接口
"""

from flask import Blueprint, request, jsonify
import logging

# 设置日志
logger = logging.getLogger(__name__)

# 创建蓝图
translate_bp = Blueprint("translate", __name__)


@translate_bp.route("/translate/upload", methods=["POST"])
def upload_excel():
    """Excel文件上传解析接口 - 返回demo数据"""
    try:
        # 检查是否有文件上传
        if "file" not in request.files:
            return jsonify({"code": -1, "message": "没有上传文件"}), 400

        file = request.files["file"]
        if file.filename == "":
            return jsonify({"code": -1, "message": "文件名为空"}), 400

        # 返回demo数据
        demo_data = {
            "code": 0,
            "message": "文件上传成功",
            "data": {
                "languages": ["key", "zh_CN", "en_US", "ja_JP", "ko_KR"],
                "language_names": {
                    "key": "键名",
                    "zh_CN": "中文",
                    "en_US": "英文",
                    "ja_JP": "日文",
                    "ko_KR": "韩文",
                },
                "translations": {
                    "app.title": {
                        "key": "app.title",
                        "zh_CN": "应用标题",
                        "en_US": "App Title",
                        "ja_JP": "",
                        "ko_KR": "",
                    },
                    "app.welcome": {
                        "key": "app.welcome",
                        "zh_CN": "欢迎使用",
                        "en_US": "Welcome",
                        "ja_JP": "",
                        "ko_KR": "",
                    },
                    "button.submit": {
                        "key": "button.submit",
                        "zh_CN": "提交",
                        "en_US": "Submit",
                        "ja_JP": "",
                        "ko_KR": "",
                    },
                    "button.cancel": {
                        "key": "button.cancel",
                        "zh_CN": "取消",
                        "en_US": "Cancel",
                        "ja_JP": "",
                        "ko_KR": "",
                    },
                    "message.success": {
                        "key": "message.success",
                        "zh_CN": "操作成功",
                        "en_US": "Success",
                        "ja_JP": "",
                        "ko_KR": "",
                    },
                },
                "metadata": {
                    "filename": file.filename,
                    "total_keys": 5,
                    "total_languages": 5,
                },
            },
        }

        return jsonify(demo_data)

    except Exception as e:
        logger.error(f"文件上传处理失败: {str(e)}")
        return jsonify({"code": -1, "message": f"服务器内部错误: {str(e)}"}), 500


@translate_bp.route("/translate", methods=["POST"])
def batch_translate():
    """批量翻译接口 - 返回demo响应"""
    try:
        # 返回demo响应
        demo_response = {
            "code": 0,
            "message": "翻译请求已接收",
            "data": {
                "task_id": "demo_task_123",
                "status": "processing",
                "message": "翻译任务已开始处理",
            },
        }

        return jsonify(demo_response)

    except Exception as e:
        logger.error(f"批量翻译处理失败: {str(e)}")
        return jsonify({"code": -1, "message": f"服务器内部错误: {str(e)}"}), 500
